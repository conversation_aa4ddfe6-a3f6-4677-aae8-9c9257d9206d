import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../lib/store';

interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  role: string;
  department: string;
  position: string;
  phone: string;
  address: string;
  hire_date: string;
  salary: number;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

interface UserContextType {
  userProfile: UserProfile | null;
  loading: boolean;
  error: string | null;
  refreshUserProfile: () => Promise<void>;
  waitForProfile: () => Promise<UserProfile | null>;
}

const UserContext = createContext<UserContextType>({
  userProfile: null,
  loading: true,
  error: null,
  refreshUserProfile: async () => { },
  waitForProfile: async () => null,
});

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const user = useAuthStore((state) => state.user);

  const fetchUserProfile = async () => {
    if (!user?.id) {
      setUserProfile(null);
      setLoading(false);
      setError(null);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError) {
        console.error('Error fetching user profile:', fetchError);
        setUserProfile(null);
        setError('Failed to load user profile');
      } else {
        setUserProfile(data);
        setError(null);
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setUserProfile(null);
      setError('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  };

  const refreshUserProfile = async () => {
    await fetchUserProfile();
  };

  const waitForProfile = async (): Promise<UserProfile | null> => {
    // If already loaded and not loading, return immediately
    if (!loading && userProfile) {
      return userProfile;
    }

    // If there's an error, return null
    if (!loading && error) {
      return null;
    }

    // Wait for loading to complete
    return new Promise((resolve) => {
      const checkLoading = () => {
        if (!loading) {
          resolve(userProfile);
        } else {
          setTimeout(checkLoading, 50);
        }
      };
      checkLoading();
    });
  };

  // Fetch user profile when user changes
  useEffect(() => {
    fetchUserProfile();
  }, [user?.id]);

  return (
    <UserContext.Provider value={{ userProfile, loading, error, refreshUserProfile, waitForProfile }}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};